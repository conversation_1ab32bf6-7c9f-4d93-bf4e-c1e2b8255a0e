export interface Testimonial {
  id: string
  name: string
  position: string
  company: string
  content: string
  rating?: number
  image?: string
}

export const testimonials: Testimonial[] = [
  {
    id: "1",
    name: "<PERSON>",
    position: "CTO",
    company: "TechInnovate Solutions",
    content:
      "Oussama's work has transformed our deployment process completely. What used to take days now happens in minutes with zero downtime. His Kubernetes expertise saved us from a major scaling crisis during our product launch.",
    rating: 5,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: "2",
    name: "<PERSON>",
    position: "VP of Engineering",
    company: "DataFlow Systems",
    content:
      "Working with Oussama on our cloud migration was the best decision we made. His methodical approach and attention to security details ensured we had zero data loss and maintained compliance throughout the transition.",
    rating: 5,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: "3",
    name: "<PERSON>",
    position: "Lead Developer",
    company: "Agile Web Services",
    content:
      "The CI/CD pipeline Oussama implemented has been a game-changer for our development team. We've reduced our release cycle from weeks to days, and the automated testing has caught critical bugs before they reached production.",
    rating: 5,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: "4",
    name: "<PERSON>",
    position: "Startup Founder",
    company: "NextGen AI",
    content:
      "As a startup with limited resources, Oussama's infrastructure as code implementation saved us thousands in operational costs while giving us enterprise-level reliability. His work allowed us to focus on product development instead of infrastructure headaches.",
    rating: 5,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: "5",
    name: "Lisa Patel",
    position: "Director of Operations",
    company: "Global Finance Tech",
    content:
      "Oussama's security-first approach to DevOps was exactly what our financial services platform needed. He implemented comprehensive monitoring and automated incident response that has proven invaluable during high-traffic periods.",
    rating: 5,
    image: "/placeholder.svg?height=100&width=100",
  },
]
