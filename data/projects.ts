export interface Project {
  id: string
  title: string
  description: string
  longDescription: string
  tags: string[]
  image: string
  githubUrl: string
  demoUrl?: string
  challenges: string[]
  solutions: string[]
  results: string[]
  technologies: {
    category: string
    items: string[]
  }[]
  screenshots: {
    url: string
    caption: string
  }[]
}

export const projects: Project[] = [
  {
    id: "automated-cicd-pipeline",
    title: "Automated CI/CD Pipeline",
    description:
      "Created a streamlined CI/CD pipeline with Jenkins and GitHub Webhooks, reducing deployment time by 50% and minimizing production issues.",
    longDescription:
      "This project involved the creation and integration of a comprehensive CI/CD pipeline using Jenkins and GitHub Webhooks. The solution was designed to automate the entire software delivery process, from code commit to production deployment, with a focus on reliability, speed, and security.",
    tags: ["Jenkins", "Docker", "AWS", "CI/CD", "GitHub"],
    image: "/placeholder.svg?height=400&width=600",
    githubUrl: "https://github.com/oussamalakhdar",
    challenges: [
      "Manual deployment processes leading to frequent human errors",
      "Long deployment times affecting development velocity",
      "Inconsistent environments between development and production",
      "Limited visibility into the deployment process",
      "Security concerns with direct access to production environments",
    ],
    solutions: [
      "Implemented Jenkins pipelines with declarative syntax for reproducible builds",
      "Created Docker containers for consistent environments across all stages",
      "Set up GitHub Webhooks for automated pipeline triggering on code changes",
      "Configured AWS EC2 instances with proper security groups for deployment",
      "Implemented automated testing stages with fail-fast approach",
    ],
    results: [
      "Reduced deployment time by 50% through automation",
      "Minimized production issues by 75% with consistent environments",
      "Improved developer productivity by eliminating manual deployment tasks",
      "Enhanced security with controlled access to production environments",
      "Increased deployment frequency from weekly to daily releases",
    ],
    technologies: [
      {
        category: "CI/CD Tools",
        items: ["Jenkins", "GitHub Webhooks", "Docker", "DockerHub"],
      },
      {
        category: "Cloud Infrastructure",
        items: ["AWS EC2", "AWS Security Groups", "AWS Networking"],
      },
      {
        category: "Automation",
        items: ["Bash Scripting", "Jenkinsfile", "Docker Compose"],
      },
      {
        category: "Testing",
        items: ["Unit Testing", "Integration Testing", "Automated Testing"],
      },
    ],
    screenshots: [
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Jenkins pipeline visualization showing build stages",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Docker container architecture for the application",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "AWS deployment infrastructure diagram",
      },
    ],
  },
  {
    id: "docker-orchestration",
    title: "Docker Orchestration Web Application",
    description:
      "Built and deployed a web application using multiple Docker containers, orchestrated with Docker Compose for seamless integration.",
    longDescription:
      "This project involved building and deploying a complete web application infrastructure using Docker containers. The solution utilized a variety of services including NGINX, MariaDB, WordPress, Redis, Adminer, and Portainer, each running in its dedicated container. The entire application was orchestrated locally using Docker Compose to ensure seamless integration and effective management of each service.",
    tags: ["Docker", "Docker Compose", "NGINX", "WordPress", "Network Administration"],
    image: "/placeholder.svg?height=400&width=600",
    githubUrl: "https://github.com/oussamalakhdar",
    challenges: [
      "Configuring multiple services to work together in isolated containers",
      "Setting up proper networking between containers",
      "Managing persistent data across container restarts",
      "Optimizing container resource usage",
      "Ensuring security between container communications",
    ],
    solutions: [
      "Created custom Docker configurations for each service",
      "Implemented Docker networks for secure service communication",
      "Set up volume mounts for persistent data storage",
      "Configured NGINX as a reverse proxy for the web services",
      "Used Docker Compose for orchestration and service dependency management",
    ],
    results: [
      "Successfully deployed a fully containerized web application",
      "Achieved isolation between services while maintaining seamless integration",
      "Implemented efficient resource utilization across containers",
      "Created a reproducible environment that can be deployed anywhere",
      "Simplified management with Portainer's container visualization",
    ],
    technologies: [
      {
        category: "Containerization",
        items: ["Docker", "Docker Compose", "Docker Networks", "Docker Volumes"],
      },
      {
        category: "Web Services",
        items: ["NGINX", "WordPress", "MariaDB", "Redis", "Adminer", "Portainer"],
      },
      {
        category: "Networking",
        items: ["Reverse Proxy", "Service Discovery", "Container Networking"],
      },
      {
        category: "System Administration",
        items: ["Linux", "Bash Scripting", "Resource Management"],
      },
    ],
    screenshots: [
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Docker Compose architecture diagram",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Portainer dashboard showing container status",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "WordPress site running in containerized environment",
      },
    ],
  },
  {
    id: "backend-web-app",
    title: "Backend Web Application with NestJS",
    description:
      "Developed a TypeScript backend web application with user account management, authentication, and real-time chat functionality.",
    longDescription:
      "This project involved the development of a robust backend web application using NestJS and TypeScript. The application features comprehensive user account functionalities, secure authentication, friendship management, and a real-time chat system. The project also included database schema design and Docker orchestration for deployment.",
    tags: ["NestJS", "TypeScript", "Prisma", "Docker", "Real-time Chat"],
    image: "/placeholder.svg?height=400&width=600",
    githubUrl: "https://github.com/oussamalakhdar",
    challenges: [
      "Designing a scalable architecture for user management and real-time communication",
      "Implementing secure authentication and authorization",
      "Creating an efficient database schema for complex relationships",
      "Building a responsive real-time chat system",
      "Containerizing the application for consistent deployment",
    ],
    solutions: [
      "Utilized NestJS framework for a modular, maintainable codebase",
      "Implemented JWT-based authentication with role-based access control",
      "Used Prisma ORM for type-safe database operations and schema management",
      "Created WebSocket-based chat system for instant message delivery",
      "Orchestrated the application with Docker for consistent environments",
    ],
    results: [
      "Successfully implemented all user account functionalities with secure authentication",
      "Created an intuitive friendship management system",
      "Developed a responsive real-time chat with message persistence",
      "Established efficient database schema for optimal data organization",
      "Achieved consistent deployment through Docker containerization",
    ],
    technologies: [
      {
        category: "Backend Framework",
        items: ["NestJS", "TypeScript", "Node.js", "Express"],
      },
      {
        category: "Database & ORM",
        items: ["Prisma", "PostgreSQL", "Database Schema Design"],
      },
      {
        category: "Authentication & Security",
        items: ["JWT", "Bcrypt", "Role-based Access Control"],
      },
      {
        category: "Real-time Communication",
        items: ["WebSockets", "Socket.io", "Event-driven Architecture"],
      },
      {
        category: "Deployment",
        items: ["Docker", "Docker Compose", "Container Networking"],
      },
    ],
    screenshots: [
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "API endpoint documentation and testing interface",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Database schema visualization",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Real-time chat system architecture",
      },
    ],
  },
  {
    id: "infrastructure-as-code-library",
    title: "Infrastructure as Code Library",
    description:
      "Created a reusable IaC library with Terraform modules for standardized cloud resource provisioning across teams.",
    longDescription:
      "The Infrastructure as Code Library is a comprehensive collection of reusable Terraform modules designed to standardize and simplify cloud resource provisioning across multiple teams and projects. This library implements organizational best practices, security controls, and compliance requirements as code, enabling consistent infrastructure deployment while allowing teams to maintain velocity and autonomy.",
    tags: ["Terraform", "AWS", "Azure", "IaC"],
    image: "/placeholder.svg?height=400&width=600",
    githubUrl: "https://github.com",
    challenges: [
      "Inconsistent infrastructure provisioning across teams",
      "Ensuring compliance with security and regulatory requirements",
      "Balancing standardization with team autonomy",
      "Managing module versioning and backward compatibility",
      "Providing comprehensive documentation for diverse user groups",
    ],
    solutions: [
      "Developed a modular Terraform library with consistent interfaces",
      "Implemented security and compliance controls as code within modules",
      "Created flexible modules with sensible defaults but configurable options",
      "Established semantic versioning with automated testing for each release",
      "Built comprehensive documentation with examples for common use cases",
    ],
    results: [
      "Reduced time to provision new environments by 75%",
      "Achieved 100% compliance with security standards across all deployments",
      "Decreased cloud infrastructure costs by 30% through standardized patterns",
      "Enabled self-service infrastructure for development teams",
      "Simplified onboarding process for new team members",
    ],
    technologies: [
      {
        category: "Infrastructure as Code",
        items: ["Terraform", "Terragrunt", "AWS CDK", "Pulumi"],
      },
      {
        category: "Cloud Providers",
        items: ["AWS", "Azure", "GCP"],
      },
      {
        category: "Testing & Validation",
        items: ["Terratest", "Checkov", "tfsec", "Sentinel"],
      },
      {
        category: "CI/CD",
        items: ["GitHub Actions", "Atlantis", "Terraform Cloud"],
      },
    ],
    screenshots: [
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Module architecture and dependency diagram",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Infrastructure deployment visualization",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Compliance dashboard showing security posture",
      },
    ],
  },
  {
    id: "serverless-microservices-platform",
    title: "Serverless Microservices Platform",
    description: "Architected a serverless platform for microservices using AWS Lambda, API Gateway, and DynamoDB.",
    longDescription:
      "The Serverless Microservices Platform is a cloud-native architecture designed to enable rapid development and deployment of scalable, event-driven microservices without managing traditional server infrastructure. This platform leverages AWS serverless technologies to provide a flexible foundation for building modern applications with automatic scaling, pay-per-use pricing, and reduced operational overhead.",
    tags: ["Serverless", "AWS Lambda", "DynamoDB", "Microservices"],
    image: "/placeholder.svg?height=400&width=600",
    githubUrl: "https://github.com",
    challenges: [
      "Designing for stateless, event-driven architecture",
      "Managing cold starts and performance optimization",
      "Implementing effective service discovery and API management",
      "Ensuring data consistency across distributed services",
      "Developing a comprehensive monitoring and debugging strategy",
    ],
    solutions: [
      "Implemented domain-driven design principles for service boundaries",
      "Used provisioned concurrency and code optimization for critical paths",
      "Created an API Gateway with custom authorizers and usage plans",
      "Designed event-sourcing patterns with DynamoDB streams",
      "Deployed X-Ray tracing with custom subsegments for observability",
    ],
    results: [
      "Reduced infrastructure costs by 60% compared to container-based solution",
      "Achieved sub-100ms response times for 95% of API requests",
      "Eliminated operational overhead for scaling during traffic spikes",
      "Decreased time-to-market for new features by 50%",
      "Improved developer productivity with streamlined deployment process",
    ],
    technologies: [
      {
        category: "Serverless Technologies",
        items: ["AWS Lambda", "AWS API Gateway", "AWS Step Functions", "AWS EventBridge", "AWS SQS", "AWS SNS"],
      },
      {
        category: "Data Storage",
        items: ["DynamoDB", "S3", "ElastiCache", "Aurora Serverless"],
      },
      {
        category: "Development & Deployment",
        items: ["Serverless Framework", "AWS SAM", "TypeScript", "Node.js"],
      },
      {
        category: "Monitoring & Observability",
        items: ["AWS CloudWatch", "X-Ray", "Lumigo", "Sentry"],
      },
    ],
    screenshots: [
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Serverless architecture diagram",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "API performance metrics dashboard",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Distributed tracing visualization",
      },
    ],
  },
  {
    id: "container-security-scanner",
    title: "Container Security Scanner",
    description:
      "Developed an automated container security scanning tool integrated with CI/CD pipelines to detect vulnerabilities.",
    longDescription:
      "The Container Security Scanner is an automated security solution designed to identify vulnerabilities, misconfigurations, and compliance issues in container images and Kubernetes deployments. This tool integrates directly into CI/CD pipelines to provide early detection of security issues, preventing vulnerable containers from reaching production environments and ensuring consistent security standards across all deployments.",
    tags: ["Docker", "Security", "CI/CD", "Python"],
    image: "/placeholder.svg?height=400&width=600",
    githubUrl: "https://github.com",
    challenges: [
      "Balancing thorough security scanning with CI/CD pipeline performance",
      "Managing false positives without compromising security",
      "Integrating with multiple container registries and CI/CD platforms",
      "Providing actionable remediation guidance for identified issues",
      "Ensuring compliance with industry security standards",
    ],
    solutions: [
      "Implemented parallel scanning with prioritized vulnerability assessment",
      "Developed a machine learning model to reduce false positives over time",
      "Created flexible adapters for major container registries and CI platforms",
      "Built an automated remediation suggestion engine with code examples",
      "Mapped findings to CIS Benchmarks, NIST, and other compliance frameworks",
    ],
    results: [
      "Reduced vulnerable containers in production by 95%",
      "Decreased mean time to remediate vulnerabilities by 70%",
      "Achieved compliance with SOC 2 and ISO 27001 security requirements",
      "Integrated with 5 major CI/CD platforms with minimal performance impact",
      "Enabled security self-service for development teams",
    ],
    technologies: [
      {
        category: "Container Security",
        items: ["Trivy", "Clair", "Anchore", "Falco", "OPA Conftest"],
      },
      {
        category: "Development",
        items: ["Python", "Go", "gRPC", "REST APIs"],
      },
      {
        category: "CI/CD Integration",
        items: ["Jenkins", "GitHub Actions", "GitLab CI", "CircleCI", "ArgoCD"],
      },
      {
        category: "Reporting & Analytics",
        items: ["Elasticsearch", "Kibana", "Grafana", "PostgreSQL"],
      },
    ],
    screenshots: [
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Vulnerability dashboard with severity breakdown",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "CI/CD pipeline integration showing security gates",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Remediation recommendations with code examples",
      },
    ],
  },
]

export function getProjectBySlug(slug: string): Project | undefined {
  return projects.find((project) => project.id === slug)
}

export function getAllProjectIds(): string[] {
  return projects.map((project) => project.id)
}
