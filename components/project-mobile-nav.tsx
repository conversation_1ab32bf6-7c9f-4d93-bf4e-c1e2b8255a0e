"use client"

import { useState } from "react"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { Menu, X, ArrowLeft, Github, Linkedin, Twitter, Server } from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface ProjectMobileNavProps {
  className?: string
}

export function ProjectMobileNav({ className }: ProjectMobileNavProps) {
  const [isOpen, setIsOpen] = useState(false)

  const toggleMenu = () => {
    setIsOpen(!isOpen)
    // Prevent scrolling when menu is open
    if (!isOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "auto"
    }
  }

  const closeMenu = () => {
    setIsOpen(false)
    document.body.style.overflow = "auto"
  }

  const navItems = [
    { href: "/#about", label: "About" },
    { href: "/#experience", label: "Experience" },
    { href: "/#projects", label: "Projects" },
    { href: "/#testimonials", label: "Testimonials" },
    { href: "/#contact", label: "Contact" },
  ]

  return (
    <div className={cn("md:hidden", className)}>
      <Button
        variant="ghost"
        size="icon"
        onClick={toggleMenu}
        className="relative z-50 rounded-full hover:bg-muted"
      >
        <AnimatePresence mode="wait" initial={false}>
          {isOpen ? (
            <motion.div
              key="close"
              initial={{ opacity: 0, rotate: -90 }}
              animate={{ opacity: 1, rotate: 0 }}
              exit={{ opacity: 0, rotate: 90 }}
              transition={{ duration: 0.2 }}
            >
              <X className="h-5 w-5 text-foreground" />
            </motion.div>
          ) : (
            <motion.div
              key="menu"
              initial={{ opacity: 0, rotate: 90 }}
              animate={{ opacity: 1, rotate: 0 }}
              exit={{ opacity: 0, rotate: -90 }}
              transition={{ duration: 0.2 }}
            >
              <Menu className="h-5 w-5 text-foreground" />
            </motion.div>
          )}
        </AnimatePresence>
        <span className="sr-only">Toggle menu</span>
      </Button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 z-40 bg-background/98 backdrop-blur-md"
          >
            <motion.div
              className="absolute top-0 right-0 p-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <Button
                variant="ghost"
                size="icon"
                onClick={closeMenu}
                className="rounded-full hover:bg-muted"
              >
                <X className="h-5 w-5 text-foreground" />
              </Button>
            </motion.div>

            <motion.nav
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1, duration: 0.2 }}
              className="flex flex-col items-center justify-center h-full space-y-6 text-center"
            >
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1, duration: 0.2 }}
                className="mb-8"
              >
                <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                  <Server className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold text-foreground">Cloud & DevOps Engineer</h3>
                <p className="text-sm text-muted-foreground">Oussama Lakhdar</p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.15, duration: 0.2 }}
                className="mb-2"
              >
                <Link
                  href="/#projects"
                  className="flex items-center justify-center gap-2 text-lg font-medium text-primary hover:text-primary/80 transition-colors px-4 py-2 rounded-md hover:bg-primary/5"
                  onClick={closeMenu}
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back to Projects
                </Link>
              </motion.div>

              <motion.div className="w-16 h-0.5 bg-muted mb-4" />

              {navItems.map((item, index) => (
                <motion.div
                  key={item.href}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 + index * 0.1, duration: 0.2 }}
                >
                  <Link
                    href={item.href}
                    className="text-xl font-medium text-foreground hover:text-primary transition-colors px-4 py-2 rounded-md hover:bg-muted/50 block"
                    onClick={closeMenu}
                  >
                    {item.label}
                  </Link>
                </motion.div>
              ))}

              <motion.div
                className="flex gap-4 mt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.2 }}
              >
                <Link href="https://github.com" target="_blank" rel="noreferrer" className="rounded-full p-2 text-muted-foreground hover:bg-muted hover:text-foreground">
                  <Github className="h-5 w-5" />
                </Link>
                <Link href="https://linkedin.com" target="_blank" rel="noreferrer" className="rounded-full p-2 text-muted-foreground hover:bg-muted hover:text-foreground">
                  <Linkedin className="h-5 w-5" />
                </Link>
                <Link href="https://twitter.com" target="_blank" rel="noreferrer" className="rounded-full p-2 text-muted-foreground hover:bg-muted hover:text-foreground">
                  <Twitter className="h-5 w-5" />
                </Link>
              </motion.div>
            </motion.nav>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
