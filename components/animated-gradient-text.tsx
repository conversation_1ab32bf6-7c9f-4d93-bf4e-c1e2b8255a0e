"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import { useTheme } from "next-themes"
import { useEffect, useState } from "react"

type GradientVariant = "default" | "primary" | "secondary" | "accent" | "blue" | "purple" | "teal"

interface AnimatedGradientTextProps {
  text: string
  className?: string
  delay?: number
  variant?: GradientVariant
}

export function AnimatedGradientText({
  text,
  className,
  delay = 0,
  variant = "default"
}: AnimatedGradientTextProps) {
  const words = text.split(" ")
  const { theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const getGradientClass = () => {
    if (!mounted) return "gradient-heading";

    const isDark = theme === "dark";

    switch (variant) {
      case "primary":
        return isDark
          ? "bg-gradient-to-r from-primary-foreground via-primary-foreground/90 to-primary-foreground/70 bg-clip-text text-transparent"
          : "bg-gradient-to-r from-blue-700 via-blue-800 to-blue-900 bg-clip-text text-transparent";
      case "secondary":
        return isDark
          ? "bg-gradient-to-r from-secondary-foreground via-secondary-foreground/90 to-secondary-foreground/70 bg-clip-text text-transparent"
          : "bg-gradient-to-r from-indigo-700 via-indigo-800 to-indigo-900 bg-clip-text text-transparent";
      case "accent":
        return isDark
          ? "bg-gradient-to-r from-accent-foreground via-accent-foreground/90 to-accent-foreground/70 bg-clip-text text-transparent"
          : "bg-gradient-to-r from-purple-700 via-purple-800 to-purple-900 bg-clip-text text-transparent";
      case "blue":
        return isDark
          ? "bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 bg-clip-text text-transparent"
          : "bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 bg-clip-text text-transparent";
      case "purple":
        return isDark
          ? "bg-gradient-to-r from-purple-400 via-purple-500 to-purple-600 bg-clip-text text-transparent"
          : "bg-gradient-to-r from-purple-600 via-purple-700 to-purple-800 bg-clip-text text-transparent";
      case "teal":
        return isDark
          ? "bg-gradient-to-r from-teal-400 via-teal-500 to-teal-600 bg-clip-text text-transparent"
          : "bg-gradient-to-r from-teal-600 via-teal-700 to-teal-800 bg-clip-text text-transparent";
      default:
        return "gradient-heading";
    }
  };

  const container = {
    hidden: { opacity: 0 },
    visible: (i = 1) => ({
      opacity: 1,
      transition: { staggerChildren: 0.12, delayChildren: delay * i },
    }),
  }

  const child = {
    hidden: {
      opacity: 0,
      y: 20,
      transition: {
        type: "spring",
        damping: 12,
        stiffness: 100,
      },
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        damping: 12,
        stiffness: 100,
      },
    },
  }

  return (
    <motion.span
      className={cn(getGradientClass(), "font-bold inline-block", className)}
      variants={container}
      initial="hidden"
      animate="visible"
    >
      {words.map((word, index) => (
        <motion.span
          key={index}
          className="inline-block"
          variants={child}
          style={{ marginRight: index === words.length - 1 ? 0 : "0.5rem" }}
        >
          {word}
        </motion.span>
      ))}
    </motion.span>
  )
}
