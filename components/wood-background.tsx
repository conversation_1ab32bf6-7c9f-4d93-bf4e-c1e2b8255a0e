"use client"

import type React from "react"

import { useTheme } from "next-themes"
import { cn } from "@/lib/utils"
import { useEffect, useState } from "react"

interface WoodBackgroundProps {
  className?: string
  children?: React.ReactNode
  variant?: "light" | "dark" | "overlay"
}

export function WoodBackground({ className, children, variant = "light" }: WoodBackgroundProps) {
  const { theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return <div className={cn("relative", className)}>{children}</div>
  }

  return (
    <div
      className={cn(
        "relative bg-cover bg-center bg-no-repeat",
        {
          "before:absolute before:inset-0 before:bg-black/40 before:z-0": variant === "overlay",
          "before:absolute before:inset-0 before:bg-black/70 before:z-0": variant === "dark" && theme === "dark",
          "before:absolute before:inset-0 before:bg-white/70 before:z-0": variant === "light" && theme === "light",
        },
        className,
      )}
      style={{
        backgroundImage: "url('/images/wood-texture.png')",
      }}
    >
      <div className="relative z-10">{children}</div>
    </div>
  )
}
