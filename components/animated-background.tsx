"use client"

import type React from "react"

import { useTheme } from "next-themes"
import { cn } from "@/lib/utils"
import { useEffect, useState } from "react"
import { motion } from "framer-motion"

interface AnimatedBackgroundProps {
  variant?: "dots" | "grid" | "waves" | "gradient"
  className?: string
  children?: React.ReactNode
}

export function AnimatedBackground({ variant = "dots", className, children }: AnimatedBackgroundProps) {
  const { theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return <div className={cn("relative", className)}>{children}</div>
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className={cn("relative overflow-hidden", className)}
    >
      {variant === "dots" && (
        <div
          className="absolute inset-0 z-0"
          style={{
            backgroundImage: `radial-gradient(circle, ${
              theme === "dark" ? "rgba(255, 255, 255, 0.15)" : "rgba(0, 0, 0, 0.1)"
            } 1px, transparent 1px)`,
            backgroundSize: "30px 30px",
            backgroundPosition: "0 0",
            opacity: 0.8,
          }}
        />
      )}

      {variant === "grid" && (
        <div
          className="absolute inset-0 z-0"
          style={{
            backgroundImage: `linear-gradient(to right, ${
              theme === "dark" ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.07)"
            } 1px, transparent 1px), 
      linear-gradient(to bottom, ${
        theme === "dark" ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.07)"
      } 1px, transparent 1px)`,
            backgroundSize: "40px 40px",
            opacity: 0.9,
          }}
        />
      )}

      {variant === "waves" && <WavesPattern />}

      {variant === "gradient" && <GradientPattern />}

      <div className="relative z-10">{children}</div>
    </motion.div>
  )
}

function WavesPattern() {
  return (
    <div className="absolute inset-0 z-0 overflow-hidden opacity-20">
      <motion.div
        className="absolute -inset-[100%]"
        animate={{ rotate: 360 }}
        transition={{ duration: 25, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
      >
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[200%] h-[200%] rounded-[40%] bg-gradient-to-r from-primary/50 to-secondary/50 blur-3xl" />
      </motion.div>
      <motion.div
        className="absolute -inset-[100%]"
        animate={{ rotate: -360 }}
        transition={{ duration: 30, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
      >
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[200%] h-[200%] rounded-[40%] bg-gradient-to-r from-secondary/40 to-primary/40 blur-3xl" />
      </motion.div>
    </div>
  )
}

function GradientPattern() {
  return (
    <div className="absolute inset-0 z-0 overflow-hidden">
      <motion.div
        className="absolute -inset-[10%]"
        animate={{ opacity: [0.6, 1, 0.6] }}
        transition={{ duration: 6, repeat: Number.POSITIVE_INFINITY, ease: "easeInOut" }}
      >
        <motion.div
          className="absolute top-0 -left-[10%] w-[40%] h-[40%] rounded-full bg-primary/20 blur-3xl"
          animate={{ x: [0, 20, 0], y: [0, -20, 0] }}
          transition={{ duration: 10, repeat: Number.POSITIVE_INFINITY, ease: "easeInOut" }}
        />
        <motion.div
          className="absolute bottom-0 right-0 w-[40%] h-[40%] rounded-full bg-secondary/20 blur-3xl"
          animate={{ x: [0, -20, 0], y: [0, 20, 0] }}
          transition={{ duration: 10, repeat: Number.POSITIVE_INFINITY, ease: "easeInOut" }}
        />
      </motion.div>
    </div>
  )
}
