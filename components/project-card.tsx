"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Github, ExternalLink } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { useMobile } from "@/hooks/use-mobile"

interface ProjectCardProps {
  id?: string
  title: string
  description: string
  tags: string[]
  image: string
  githubUrl: string
  demoUrl?: string
  index?: number
}

export default function ProjectCard({
  id,
  title,
  description,
  tags,
  image,
  githubUrl,
  demoUrl,
  index = 0,
}: ProjectCardProps) {
  const isMobile = useMobile();

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="touch-manipulation"
    >
      <Card className="overflow-hidden card-hover h-full">
        <Link href={id ? `/projects/${id}` : "#"} className="block">
          <div className="aspect-video relative overflow-hidden">
            <Image
              src={image || "/placeholder.svg"}
              alt={title}
              fill
              className="object-cover transition-transform hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>
        </Link>
        <CardContent className="p-3 sm:p-4">
          <Link href={id ? `/projects/${id}` : "#"} className="block">
            <h3 className="text-lg sm:text-xl font-bold hover:text-primary transition-colors line-clamp-1">{title}</h3>
          </Link>
          <p className="mt-1 sm:mt-2 text-sm sm:text-base text-muted-foreground line-clamp-2">{description}</p>
          <div className="mt-3 sm:mt-4 flex flex-wrap gap-1.5 sm:gap-2">
            {tags.slice(0, isMobile ? 3 : 4).map((tag, i) => (
              <motion.div
                key={tag}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: i * 0.1 + 0.2 }}
              >
                <Badge variant="secondary" className="text-xs sm:text-sm py-0 px-1.5 sm:px-2">{tag}</Badge>
              </motion.div>
            ))}
            {tags.length > (isMobile ? 3 : 4) && (
              <Badge variant="outline" className="text-xs sm:text-sm py-0 px-1.5 sm:px-2">+{tags.length - (isMobile ? 3 : 4)}</Badge>
            )}
          </div>
        </CardContent>
        <CardFooter className="p-3 sm:p-4 pt-0 flex justify-between">
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button variant="outline" size="sm" asChild className="h-8 text-xs sm:text-sm px-2 sm:px-3">
              <Link href={githubUrl} target="_blank" rel="noreferrer" className="gap-1">
                <Github className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="hidden xs:inline">Code</span>
              </Link>
            </Button>
          </motion.div>
          {id ? (
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                size="sm"
                asChild
                className="bg-gradient-to-r from-primary to-secondary hover:opacity-90 h-8 text-xs sm:text-sm px-2 sm:px-3"
              >
                <Link href={`/projects/${id}`} className="gap-1">
                  <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4" />
                  Details
                </Link>
              </Button>
            </motion.div>
          ) : demoUrl ? (
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button variant="outline" size="sm" asChild className="h-8 text-xs sm:text-sm px-2 sm:px-3">
                <Link href={demoUrl} target="_blank" rel="noreferrer" className="gap-1">
                  <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden xs:inline">Demo</span>
                </Link>
              </Button>
            </motion.div>
          ) : null}
        </CardFooter>
      </Card>
    </motion.div>
  )
}
