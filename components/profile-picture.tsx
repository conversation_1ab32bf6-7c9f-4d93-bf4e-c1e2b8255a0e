"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { useMobile } from "@/hooks/use-mobile"
import { useTheme } from "next-themes"
import { useEffect, useState } from "react"
import { Download } from "lucide-react"
import { Button } from "@/components/ui/button"
import Link from "next/link"

interface ProfilePictureProps {
  className?: string
  showDownloadButton?: boolean
}

export function ProfilePicture({ className, showDownloadButton = false }: ProfilePictureProps) {
  const isMobile = useMobile()
  const { theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div className={`${className || ""} relative`}>
        <div
          className={`relative overflow-hidden rounded-full border-4 border-gray-200 bg-background shadow-xl ${
            isMobile ? "w-[180px] h-[180px]" : "w-[300px] h-[300px]"
          }`}
        />
      </div>
    )
  }

  return (
    <motion.div
      className={`${className || ""} relative`}
      initial={{ opacity: 0 }}
      animate={{
        opacity: 1,
        transition: {
          duration: 0.6,
          ease: "easeOut"
        }
      }}
    >
      <div
        className={`relative overflow-hidden rounded-full border-4 ${
          theme === "dark" ? "border-white/10" : "border-gray-200"
        } bg-background shadow-xl ${isMobile ? "w-[180px] h-[180px]" : "w-[300px] h-[300px]"}`}
      >
        <Image
          src="/images/profile.png"
          alt="Oussama Lakhdar - Cloud DevOps Engineer"
          fill
          className="object-cover"
          priority
        />
      </div>

      {showDownloadButton && (
        <motion.div
          className="absolute -bottom-8 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0 }}
          animate={{
            opacity: 1,
            transition: {
              delay: 0.8,
              duration: 0.4,
              ease: "easeOut"
            }
          }}
        >
          <Link href="/resume.pdf" target="_blank" rel="noopener noreferrer">
            <Button
              size="default"
              variant="outline"
              className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm border-2 border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-950 hover:border-blue-300 dark:hover:border-blue-700 shadow-lg hover:shadow-xl transition-all duration-200 flex items-center gap-2 font-medium text-blue-700 dark:text-blue-300"
            >
              <Download className="h-4 w-4" />
              <span>Download Resume</span>
            </Button>
          </Link>
        </motion.div>
      )}
    </motion.div>
  )
}
