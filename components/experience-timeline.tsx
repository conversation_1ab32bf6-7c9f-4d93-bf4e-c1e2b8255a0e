import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function ExperienceTimeline() {
  const experiences = [
    {
      title: "DevOps Engineer Intern",
      company: "The UM6P Vanguard Center",
      period: "03/2024 - 09/2024",
      description:
        "Working on automating CI/CD pipelines, provisioning Kubernetes clusters, and implementing infrastructure solutions.",
      achievements: [
        "Automated CI/CD pipelines with GitHub Actions, cutting deployment time by 40%",
        "Provisioned scalable Kubernetes clusters on VMs using bash scripting and Ansible",
        "Implemented NFS server for Kubernetes persistent storage, reducing recovery times by 50%",
        "Streamlined application delivery by integrating Argo CD, reducing downtime by 60%",
        "Optimized software delivery pipelines by collaborating with cross-functional teams",
      ],
      technologies: ["Kubernetes", "Docker", "GitHub Actions", "Ansible", "ArgoCD", "Bash", "Linux"],
    },
    {
      title: "Project: Automated CI/CD Pipeline",
      company: "Personal Project",
      period: "2023",
      description:
        "Created and integrated a streamlined CI/CD pipeline with Jenkins and GitHub Webhooks.",
      achievements: [
        "Reduced deployment time by 50% and minimized production issues by 75%",
        "Orchestrated end-to-end automation for software delivery on AWS EC2 instances",
        "Configured AWS security groups and network settings for secure browser-based access",
      ],
      technologies: ["Jenkins", "Docker", "DockerHub", "AWS", "GitHub Webhooks"],
    },
    {
      title: "Project: Backend Web App",
      company: "Personal Project",
      period: "2023",
      description: "Developed a TypeScript web app backend with user account functionalities and real-time chat.",
      achievements: [
        "Spearheaded development of user account functionalities, authentication, and friendship management",
        "Contributed to database schema design for efficient data organization",
        "Crafted endpoints for seamless communication within the application",
        "Architected a real-time chat system for instant and interactive communication",
        "Led Docker orchestration for deployment with optimized container communication",
      ],
      technologies: ["NestJS", "TypeScript", "Prisma", "Docker", "Node.js"],
    },
  ]

  return (
    <div className="space-y-8">
      {experiences.map((exp, index) => (
        <div key={index} className="relative pl-6 pb-8">
          {index !== experiences.length - 1 && (
            <div className="absolute top-6 left-2 bottom-0 w-0.5 bg-muted-foreground/20"></div>
          )}
          <div className="absolute top-6 left-0 w-4 h-4 rounded-full border-2 border-primary bg-background"></div>
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-bold">{exp.title}</h3>
                    <Badge variant="outline">{exp.period}</Badge>
                  </div>
                  <p className="text-muted-foreground">{exp.company}</p>
                </div>
                <p>{exp.description}</p>
                <div>
                  <h4 className="font-semibold mb-2">Key Achievements:</h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {exp.achievements.map((achievement, i) => (
                      <li key={i} className="text-sm">
                        {achievement}
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="flex flex-wrap gap-2">
                  {exp.technologies.map((tech) => (
                    <Badge key={tech} variant="secondary">
                      {tech}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ))}
    </div>
  )
}
