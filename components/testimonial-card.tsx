"use client"

import { useState } from "react"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Star } from "lucide-react"
import { cn } from "@/lib/utils"
import type { Testimonial } from "@/data/testimonials"
import { motion } from "framer-motion"

interface TestimonialCardProps {
  testimonial: Testimonial
  className?: string
}

export function TestimonialCard({ testimonial, className }: TestimonialCardProps) {
  const [imageError, setImageError] = useState(false)

  return (
    <motion.div
      whileHover={{ y: -5 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className={cn("card-hover h-full", className)}>
        <CardContent className="p-6 flex flex-col h-full">
          <div className="flex items-center gap-4 mb-4">
            <motion.div
              className="relative h-12 w-12 rounded-full overflow-hidden bg-muted"
              whileHover={{ scale: 1.1 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              {!imageError ? (
                <Image
                  src={testimonial.image || "/placeholder.svg?height=100&width=100"}
                  alt={testimonial.name}
                  fill
                  className="object-cover"
                  onError={() => setImageError(true)}
                />
              ) : (
                <div className="h-full w-full flex items-center justify-center bg-primary/10 text-primary font-medium text-lg">
                  {testimonial.name.charAt(0)}
                </div>
              )}
            </motion.div>
            <div>
              <h4 className="font-semibold">{testimonial.name}</h4>
              <p className="text-sm text-muted-foreground">
                {testimonial.position}, {testimonial.company}
              </p>
            </div>
          </div>

          {testimonial.rating && (
            <div className="flex mb-3">
              {Array.from({ length: 5 }).map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: i * 0.1, type: "spring", stiffness: 400, damping: 10 }}
                >
                  <Star
                    className={cn(
                      "h-4 w-4",
                      i < testimonial.rating! ? "text-yellow-500 fill-yellow-500" : "text-muted",
                    )}
                  />
                </motion.div>
              ))}
            </div>
          )}

          <motion.blockquote
            className="text-muted-foreground italic flex-grow"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <span className="text-3xl text-primary">"</span>
            {testimonial.content}
            <span className="text-3xl text-primary">"</span>
          </motion.blockquote>
        </CardContent>
      </Card>
    </motion.div>
  )
}
