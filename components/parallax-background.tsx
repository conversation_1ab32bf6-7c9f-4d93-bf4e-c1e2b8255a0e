"use client"

import type React from "react"

import { useRef } from "react"
import { motion, useScroll, useTransform } from "framer-motion"
import { cn } from "@/lib/utils"

interface ParallaxBackgroundProps {
  className?: string
  children?: React.ReactNode
  bgClassName?: string
  speed?: number
}

export function ParallaxBackground({ className, children, bgClassName, speed = 0.2 }: ParallaxBackgroundProps) {
  const ref = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  })

  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", `${30 * speed}%`])

  return (
    <div ref={ref} className={cn("relative overflow-hidden", className)}>
      <motion.div className={cn("absolute inset-0 w-full h-full -z-10", bgClassName)} style={{ y: backgroundY }} />
      <div className="relative z-10">{children}</div>
    </div>
  )
}
