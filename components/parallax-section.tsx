"use client"

import { useRef, type ReactNode } from "react"
import { motion, useScroll, useTransform } from "framer-motion"
import { cn } from "@/lib/utils"

interface ParallaxSectionProps {
  children: ReactNode
  className?: string
  offset?: number
  direction?: "up" | "down" | "left" | "right"
  speed?: number
}

export function ParallaxSection({
  children,
  className,
  offset = 50,
  direction = "up",
  speed = 0.5,
}: ParallaxSectionProps) {
  const ref = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  })

  // Calculate transform values based on direction
  const getTransformValues = () => {
    const multiplier = offset * speed
    switch (direction) {
      case "up":
        return { y: [multiplier, -multiplier] }
      case "down":
        return { y: [-multiplier, multiplier] }
      case "left":
        return { x: [multiplier, -multiplier] }
      case "right":
        return { x: [-multiplier, multiplier] }
      default:
        return { y: [multiplier, -multiplier] }
    }
  }

  const transformProps = getTransformValues()
  const yRange = transformProps.y || [0, 0]
  const xRange = transformProps.x || [0, 0]

  const y = useTransform(scrollYProgress, [0, 1], yRange)
  const x = useTransform(scrollYProgress, [0, 1], xRange)

  return (
    <div ref={ref} className={cn("relative overflow-hidden", className)}>
      <motion.div style={{ y, x }} className="w-full h-full">
        {children}
      </motion.div>
    </div>
  )
}
