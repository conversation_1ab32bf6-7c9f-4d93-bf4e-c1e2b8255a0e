"use client"

import { motion } from "framer-motion"
import { useInView } from "react-intersection-observer"
import { cn } from "@/lib/utils"

interface SkillProgressBarProps {
  name: string
  percentage: number
  color?: string
  delay?: number
  className?: string
}

export function SkillProgressBar({ name, percentage, color = "primary", delay = 0, className }: SkillProgressBarProps) {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const variants = {
    hidden: { width: 0 },
    visible: {
      width: `${percentage}%`,
      transition: {
        duration: 1.2,
        delay,
        ease: [0.25, 0.1, 0.25, 1],
      },
    },
  }

  const textVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        delay: delay + 0.2,
      },
    },
  }

  const percentageVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        delay: delay + 1,
      },
    },
  }

  return (
    <div ref={ref} className={cn("mb-4", className)}>
      <div className="flex justify-between mb-1">
        <motion.span
          className="text-sm font-medium"
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={textVariants}
        >
          {name}
        </motion.span>
        <motion.span
          className="text-sm font-medium"
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={percentageVariants}
        >
          {percentage}%
        </motion.span>
      </div>
      <div className="h-2 bg-muted rounded-full overflow-hidden">
        <motion.div
          className={cn(`h-full rounded-full bg-${color}`)}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={variants}
        />
      </div>
    </div>
  )
}
