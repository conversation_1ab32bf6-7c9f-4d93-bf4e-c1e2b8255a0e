"use client"

import { Card, CardContent } from "@/components/ui/card"
import { SkillProgressBar } from "@/components/skill-progress-bar"
import { ScrollReveal } from "@/components/scroll-reveal"
import { motion } from "framer-motion"

interface SkillCategory {
  name: string
  skills: {
    name: string
    percentage: number
  }[]
}

export function SkillsSection() {
  const skillCategories: SkillCategory[] = [
    {
      name: "Cloud Platforms",
      skills: [
        { name: "AWS", percentage: 95 },
        { name: "Azure", percentage: 85 },
        { name: "Google Cloud Platform", percentage: 80 },
      ],
    },
    {
      name: "Containerization & Orchestration",
      skills: [
        { name: "Kubernet<PERSON>", percentage: 90 },
        { name: "<PERSON><PERSON>", percentage: 95 },
        { name: "<PERSON><PERSON>", percentage: 85 },
      ],
    },
    {
      name: "CI/CD & Automation",
      skills: [
        { name: "<PERSON>", percentage: 90 },
        { name: "GitHub Actions", percentage: 95 },
        { name: "GitLab CI", percentage: 85 },
        { name: "ArgoCD", percentage: 80 },
      ],
    },
    {
      name: "Infrastructure as Code",
      skills: [
        { name: "Terraform", percentage: 95 },
        { name: "Ansible", percentage: 85 },
        { name: "CloudFormation", percentage: 80 },
        { name: "Pulumi", percentage: 75 },
      ],
    },
    {
      name: "Monitoring & Observability",
      skills: [
        { name: "Prometheus", percentage: 90 },
        { name: "Grafana", percentage: 85 },
        { name: "ELK Stack", percentage: 80 },
        { name: "Datadog", percentage: 75 },
      ],
    },
    {
      name: "Programming",
      skills: [
        { name: "Python", percentage: 90 },
        { name: "Go", percentage: 75 },
        { name: "Bash", percentage: 85 },
        { name: "JavaScript/TypeScript", percentage: 80 },
      ],
    },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      {skillCategories.map((category, categoryIndex) => (
        <ScrollReveal key={category.name} delay={categoryIndex * 0.1}>
          <Card className="card-hover h-full">
            <CardContent className="p-6">
              <motion.h3
                className="text-xl font-bold mb-4"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: categoryIndex * 0.1 }}
              >
                {category.name}
              </motion.h3>
              <div className="space-y-4">
                {category.skills.map((skill, skillIndex) => (
                  <SkillProgressBar
                    key={skill.name}
                    name={skill.name}
                    percentage={skill.percentage}
                    delay={skillIndex * 0.2 + 0.1}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        </ScrollReveal>
      ))}
    </div>
  )
}
